<div>
  <ion-header mode="ios">
    <ion-toolbar color="primary" mode="ios">
      <ion-title>{{ pageHeading | translate }}</ion-title>
      <ion-buttons slot="end">
        <ion-button (click)="closeModal()"> <ion-icon slot="icon-only" name="close"></ion-icon></ion-button>
      </ion-buttons>
    </ion-toolbar>
    <ion-progress-bar type="indeterminate" *ngIf="progressbar"></ion-progress-bar>
  </ion-header>

  <!-- Tabs navigation with styling to match the master-data page -->
  <ion-segment [(ngModel)]="segmentValue" mode="md" class="master-data-tabs" value="Details">
    <ion-segment-button value="Details" layout="icon-start" class="segment-button-full-width">
      <ion-label>User Details</ion-label>
    </ion-segment-button>
    <ion-segment-button value="Skills" layout="icon-start" class="segment-button-full-width">
      <ion-label>User Skills</ion-label>
    </ion-segment-button>
  </ion-segment>
    <div [ngSwitch]="segmentValue" class="tab-content">
    <div *ngSwitchCase="'Details'" class="details-tab-content">
      <div class="form-container">
        <div class="form-row">
          <div class="form-group">
            <ion-select style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 5px;" interface="popover"
              placeholder="Please select a role" fill="outline" required="true" labelPlacement="stacked"
              [(ngModel)]="selectedRole">
              <div slot="label">Select Role <ion-text color="danger">*</ion-text></div>
              <ion-icon slot="start" name="people-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
              <ion-select-option *ngFor="let role of roles" [value]="role.ROLE_NAME">{{role.ROLE_NAME}}</ion-select-option>
            </ion-select>
            <div *ngIf="!isUpdatingContent && !selectedRole" style="font-size: 12px; color: #666; margin-top: -3px; margin-bottom: 8px;">
              Please select a role for this user
            </div>
          </div>

          <div class="form-group">
            <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 5px;"
              placeholder="Enter User ID" [disabled]="isUpdatingContent" [(ngModel)]="user.USER_ID"
              required="true" labelPlacement="stacked" fill="outline">
              <div slot="label">User ID <ion-text color="danger">*</ion-text></div>
              <ion-icon slot="start" name="id-card-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
            </ion-input>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 5px;"
              placeholder="Enter First Name" [(ngModel)]="user.FIRST_NAME" (ionChange)="formUserId()" (ionInput)="clearError()"
              required="true" labelPlacement="stacked" fill="outline">
              <div slot="label">First Name <ion-text color="danger">*</ion-text></div>
              <ion-icon slot="start" name="person-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
            </ion-input>
          </div>

          <div class="form-group">
            <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 5px;"
              placeholder="Enter Last Name" [(ngModel)]="user.LAST_NAME" (ionChange)="formUserId()" (ionInput)="clearError()"
              required="true" labelPlacement="stacked" fill="outline">
              <div slot="label">Last Name <ion-text color="danger">*</ion-text></div>
              <ion-icon slot="start" name="person-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
            </ion-input>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 2px;"
              placeholder="Enter Email" [disabled]="isUpdatingContent" [(ngModel)]="user.EMAIL"
              required="true" labelPlacement="stacked" fill="outline" type="email"
              [class.ion-invalid]="!emailValid" [class.ion-valid]="emailValid"
              (ionInput)="validateEmail(); clearError()" (ionBlur)="validateEmail()">
              <div slot="label">Email <ion-text color="danger">*</ion-text></div>
              <ion-icon slot="start" name="mail-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
            </ion-input>
            <div *ngIf="!emailValid" class="validation-error">
              {{ emailErrorMessage }}
            </div>
          </div>

          <div class="form-group">
            <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 2px;"
              placeholder="Enter Phone Number (xxx-xxx-xxxx)" [(ngModel)]="user.PHONE"
              required="true" labelPlacement="stacked" fill="outline" maxlength="12"
              (ionInput)="formatPhoneNumber($event)" (ionBlur)="validatePhoneNumber()"
              [class.ion-invalid]="!phoneValid" [class.ion-valid]="phoneValid">
              <div slot="label">Phone Number <ion-text color="danger">*</ion-text></div>
              <ion-icon slot="start" name="call-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
            </ion-input>
            <div *ngIf="!phoneValid" class="validation-error">
              {{ phoneErrorMessage }}
            </div>
          </div>
        </div>

        <div class="approvals-section">
          <ion-select style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 5px;" interface="popover"
            placeholder="Select Approvals" fill="outline" labelPlacement="stacked" multiple="true"
            [(ngModel)]="selectedApprovals" (ionChange)="updateApprovalChecks()">
            <div slot="label">Select Approvals</div>
            <ion-icon slot="start" name="checkmark-circle-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
            <ion-select-option *ngFor="let approval of approvalsList" [value]="approval.APPR_TYPE">{{approval.APPR_TYPE}}</ion-select-option>
          </ion-select>

          <!-- Display selected approvals as badges -->
          <div class="approval-badges" *ngIf="selectedApprovals && selectedApprovals.length > 0">
            <ion-badge *ngFor="let approval of selectedApprovals" color="primary" class="approval-badge">
              {{ approval }}
              <ion-icon name="close-circle" (click)="removeApproval(approval)"></ion-icon>
            </ion-badge>
          </div>
        </div>
      </div>
    </div>
    <div *ngSwitchCase="'Skills'" class="skills-tab-content">
      <!-- Search card -->
      <div class="search-card">
        <div class="search-wrapper">
          <ion-icon name="search-outline"></ion-icon>
          <input type="text" placeholder="Search skills..." [(ngModel)]="skillSearchTerm" (input)="filterSkills()">
        </div>
        <div class="action-buttons">
          <button class="add-btn" (click)="openSkillModal(false, '')">
            <ion-icon name="add-outline"></ion-icon>
            Add Skill
          </button>
        </div>
      </div>

      <!-- Skills Grid -->
      <div class="custom-table">
        <!-- Table Header -->
        <div class="table-header">
          <div class="header-col skill-col">Skill</div>
          <div class="header-col rating-col">Rating</div>
          <div class="header-col cert-col">Certificate</div>
        </div>

          <!-- Skills List -->
          <div class="skills-list">
            <ion-card *ngFor="let skill of filteredSkills; index as i" class="skill-card unselected-card">
              <div class="table-row">
                <!-- Skill Type Column -->
                <div class="table-col skill-col">
                  <div class="skill-container">
                    <span>{{ skill.SKILL_TYPE }}</span>
                  </div>
                </div>

                <!-- Rating Column -->
                <div class="table-col rating-col">
                  <div class="rating-display">
                    <ng-container *ngFor="let star of [1,2,3,4,5]; let j = index">
                      <ion-icon [name]="j < +skill.RATING ? 'star' : 'star-outline'" class="star-icon">
                      </ion-icon>
                    </ng-container>
                  </div>
                </div>

                <!-- Certificates Column -->
                <div class="table-col cert-col">
                  <div class="cert-content">
                    <ng-container *ngFor="let image of getFilteredDocs(skill.SKILL_TYPE); index as j">
                      <div *ngIf="image?.docItem?.FILE_NAME" class="certificate-item">
                        <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'EXCEL'"
                          class="doc-link" (click)="downloadFile(image); $event.stopPropagation();">
                          <ion-icon name="document-outline"></ion-icon>
                          {{ image.docItem.FILE_NAME }}
                        </a>

                        <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'PPT'"
                          class="doc-link" (click)="downloadFile(image); $event.stopPropagation();">
                          <ion-icon name="document-outline"></ion-icon>
                          {{ image.docItem.FILE_NAME }}
                        </a>

                        <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'WORD'"
                          class="doc-link" (click)="downloadFile(image); $event.stopPropagation();">
                          <ion-icon name="document-outline"></ion-icon>
                          {{ image.docItem.FILE_NAME }}
                        </a>

                        <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'DOCUMENT'"
                          class="doc-link" (click)="downloadFile(image); $event.stopPropagation();">
                          <ion-icon name="document-outline"></ion-icon>
                          {{ image.docItem.FILE_NAME }}
                        </a>

                        <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'PDF'"
                          class="doc-link" (click)="downloadFile(image); $event.stopPropagation();">
                          <ion-icon name="document-outline"></ion-icon>
                          {{ image.docItem.FILE_NAME }}
                        </a>

                        <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'TEXT'"
                          class="doc-link" (click)="downloadFile(image); $event.stopPropagation();">
                          <ion-icon name="document-outline"></ion-icon>
                          {{ image.docItem.FILE_NAME }}
                        </a>

                        <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'CSV'"
                          class="doc-link" (click)="downloadFile(image); $event.stopPropagation();">
                          <ion-icon name="document-outline"></ion-icon>
                          {{ image.docItem.FILE_NAME }}
                        </a>

                        <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'URL'"
                          [href]="image.docItem.FILE_NAME" target="_blank" rel="noopener noreferrer" class="doc-link"
                          (click)="$event.stopPropagation();">
                          <ion-icon name="link-outline"></ion-icon>
                          {{ image.docItem.TITLE }}
                        </a>

                        <span *ngIf="image?.DOC_TYPE === 'IMAGE'" class="doc-link">
                          <ion-icon name="image-outline"></ion-icon>
                          {{ image?.docItem?.FILE_NAME }}
                        </span>
                      </div>
                    </ng-container>
                  </div>
                  <!-- Row Action Buttons -->
                  <div class="row-actions">
                    <ion-button fill="clear" size="small" color="primary" (click)="openSkillModal(true, skill); $event.stopPropagation();">
                      <ion-icon name="create-outline"></ion-icon>
                    </ion-button>
                    <ion-button fill="clear" size="small" color="danger" (click)="deleteSelectedItem(skill, i); $event.stopPropagation();">
                      <ion-icon name="trash-outline"></ion-icon>
                    </ion-button>
                  </div>
                </div>
              </div>
            </ion-card>

            <!-- Empty state message if no skills -->
            <div *ngIf="filteredSkills.length === 0" class="empty-skills-message">
              <p *ngIf="!userSkill || userSkill.length === 0">No skills added yet. Click 'Add Skill' to add skills.</p>
              <p *ngIf="userSkill && userSkill.length > 0">No matching skills found. Try a different search term.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <p class="error">{{displayError | translate}}</p>
</div>
<ion-footer mode="ios">
  <ion-toolbar mode="ios">
    <ion-button slot="end" color="danger" mode="md" (click)="closeModal()">Cancel</ion-button>
    <ion-button slot="end" color="success" mode="md" [disabled]="!user?.FIRST_NAME || !user?.LAST_NAME || !user?.EMAIL || !user?.PHONE || !selectedRole" (click)="addOrUpdateUser(user)">Save</ion-button>
  </ion-toolbar>
</ion-footer>
