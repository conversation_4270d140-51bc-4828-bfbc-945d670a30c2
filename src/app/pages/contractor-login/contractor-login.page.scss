.main-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.carmeuse-logo {
  width: 230px;
  margin-bottom: 20px;
}

.project-heading {
  margin: 2% 0 0%;
  font-weight: 600;
}

.login-button {
  width: 230px;
  margin-bottom: 2%;
}

// ion-input {
//   width: 262px;
// }

.inputWithUrl {
  width: 100%;
}

.forgotPassword {
  margin: 10px;
}

a {
  text-decoration: none;
  color: #3f51b5;
}

ion-progress-bar {
  width: 274px;
}

.mainBlock {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.versionBlock {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.version {
  font-size: 12px;
  margin-bottom: 1%;
  color: slategrey;
  font-family: unset;
}

.url {
  margin-right: 0;
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0;
  width: 294px;
}

.serverUrlList {
  margin-left: 1px;
  cursor: pointer;
  color: #0c960c;
  display: flex;
  align-items: center;
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0;
}

.changeServer {
  padding: 10px;
}

// .email,
// .password {
//   width: 364px;
// }

.textCenter {
  text-align: center;
  margin-bottom: 20px;
}


#form {
  #email {
      margin-bottom: 16px;
  }

  #password {
      margin-top: 24px;
      margin-bottom: 24px;
  }

  #forgotPassword {
      text-align: right;

      p {
          font-size: 14px;
          color: #1A374D;
          margin-bottom: 24px;
          line-height: 24px;
          text-decoration: underline;
      }
  }
}

#password-reset-form {
  #new-password {
      margin-bottom: 16px;
  }

  #confirm-password {
      margin-bottom: 16px;
  }

  #reset-token {
      margin-bottom: 24px;
  }

  #change-password-button {
      margin-bottom: 16px;
  }

  #back-to-login {
      text-align: center;
  }
}