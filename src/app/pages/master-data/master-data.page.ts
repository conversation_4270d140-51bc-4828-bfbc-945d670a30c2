import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import {
  ResultType,
  UnviredCordovaSDK,
} from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import {
  <PERSON><PERSON><PERSON>ontroller,
  LoadingController,
  Modal<PERSON>ontroller,
  IonContent,
} from '@ionic/angular';
import { MasterDataDetailsComponent } from 'src/app/components/master-data-details/master-data-details.component';
import {
  APPROVAL_TYPE_HEADER,
  PERMIT_TYPE_HEADER,
  ROLE_HEADER,
  STRUCTURE_CAT_HEADER,
  STRUCTURE_STATUS_HEADER,
  STRUCTURE_TYPE_HEADER,
  SKILL_HEADER,
} from 'src/app/data-models/data_classes';
import { DataService } from 'src/app/services/data.service';
import {
  MasterDataType,
  MasterSelectedMode,
} from 'src/app/shared/app-constants';

@Component({
  selector: 'app-master-data',
  templateUrl: './master-data.page.html',
  styleUrls: ['./master-data.page.scss'],
})
export class MasterDataPage implements OnInit {
  @ViewChild(IonContent) content: IonContent;

  public selectedTab: string;
  public title: string;
  public structureTypesList: STRUCTURE_TYPE_HEADER[] = [];
  public filteredStructureTypesList: STRUCTURE_TYPE_HEADER[] = [];
  public structureCategoriesList: STRUCTURE_CAT_HEADER[] = [];
  public filteredStructureCategoriesList: STRUCTURE_CAT_HEADER[] = [];
  public structureStatusList: STRUCTURE_STATUS_HEADER[] = [];
  public filteredStructureStatusList: STRUCTURE_STATUS_HEADER[] = [];
  public permitTypesList: PERMIT_TYPE_HEADER[] = [];
  public filteredPermitTypesList: PERMIT_TYPE_HEADER[] = [];
  public approvalTypesList: APPROVAL_TYPE_HEADER[] = [];
  public filteredApprovalTypesList: APPROVAL_TYPE_HEADER[] = [];
  public rolesList: ROLE_HEADER[] = [];
  public filteredRolesList: ROLE_HEADER[] = [];
  public skillsList: SKILL_HEADER[] = [];
  public filteredSkillsList: SKILL_HEADER[] = [];
  public isShowProgressBar: boolean = false;
  public rolesErrorMsg: string;
  public role: any;
  public roleUpdates: any = [];
  public searchTerm: string = '';

  constructor(
    public modalController: ModalController,
    private unviredSDK: UnviredCordovaSDK,
    private loadingController: LoadingController,
    private dataService: DataService,
    private alertController: AlertController
  ) {
    this.selectedTab = 'structureTypes';
    this.title = 'Structure Types';
    this.isShowProgressBar = false;
  }

  ngOnInit() { }

  // Convert color number to hex string for the color picker
  getColorHex(colorNumber: number): string {
    if (!colorNumber) return '#3880ff'; // Default Ionic blue

    // Convert number to hex string and ensure it has 6 digits
    let hexString = colorNumber.toString(16);
    hexString = hexString.padStart(6, '0');

    return '#' + hexString;
  }

  // Filter data based on search term
  filterData() {
    const searchTerm = this.searchTerm.toLowerCase().trim();

    if (!searchTerm) {
      // If search term is empty, show all data
      this.filteredStructureTypesList = [...this.structureTypesList];
      this.filteredStructureCategoriesList = [...this.structureCategoriesList];
      this.filteredStructureStatusList = [...this.structureStatusList];
      this.filteredPermitTypesList = [...this.permitTypesList];
      this.filteredApprovalTypesList = [...this.approvalTypesList];
      this.filteredRolesList = [...this.rolesList];
      this.filteredSkillsList = [...this.skillsList];
      return;
    }

    // Filter structure types
    this.filteredStructureTypesList = this.structureTypesList.filter(item => {
      return item.STRUCT_TYPE?.toLowerCase().includes(searchTerm) ||
             item.DESCRIPTION?.toLowerCase().includes(searchTerm);
    });

    // Filter structure categories
    this.filteredStructureCategoriesList = this.structureCategoriesList.filter(item => {
      return item.CATEGORY?.toLowerCase().includes(searchTerm) ||
             item.DESCRIPTION?.toLowerCase().includes(searchTerm);
    });

    // Filter structure status
    this.filteredStructureStatusList = this.structureStatusList.filter(item => {
      return item.STATUS?.toLowerCase().includes(searchTerm) ||
             item.DESCRIPTION?.toLowerCase().includes(searchTerm);
    });

    // Filter permit types
    this.filteredPermitTypesList = this.permitTypesList.filter(item => {
      return item.PERMIT_TYPE?.toLowerCase().includes(searchTerm) ||
             item.DESCRIPTION?.toLowerCase().includes(searchTerm) ||
             item.PREFIX?.toLowerCase().includes(searchTerm) ||
             item.FORM_ID?.toLowerCase().includes(searchTerm) ||
             item.FORM_NAME?.toLowerCase().includes(searchTerm);
    });

    // Filter approval types
    this.filteredApprovalTypesList = this.approvalTypesList.filter(item => {
      return item.APPR_TYPE?.toLowerCase().includes(searchTerm) ||
             item.SCOPE?.toLowerCase().includes(searchTerm) ||
             item.DESCRIPTION?.toLowerCase().includes(searchTerm);
    });

    // Filter roles
    this.filteredRolesList = this.rolesList.filter(item => {
      return item.ROLE_NAME?.toLowerCase().includes(searchTerm);
    });

    // Filter skills
    this.filteredSkillsList = this.skillsList.filter(item => {
      return item.SKILL_TYPE?.toLowerCase().includes(searchTerm) ||
             item.DESCRIPTION?.toLowerCase().includes(searchTerm);
    });
  }

  ngAfterViewInit() {
    // Ensure content is scrollable
    setTimeout(() => {
      if (this.content) {
        this.content.scrollToTop(0);
        this.content.getScrollElement().then(scrollEl => {
          scrollEl.style.overflowY = 'auto';
          scrollEl.style.height = '100%';
          // Fix for TypeScript error - use a type assertion
          (scrollEl.style as any)['-webkit-overflow-scrolling'] = 'touch';
        });
      }
    }, 100);
  }

  async ionViewDidEnter() {
    this.isShowProgressBar = true;
    await this.fetchMasterData(
      MasterDataType.structureTypes,
      'Structure Types',
      true
    );
    setTimeout(() => {
      this.isShowProgressBar = false;
    }, 300);
  }

  // Fetch Master Data Based On Type
  async fetchMasterData(type: any, name: string, isLoaderPresent: boolean) {
    this.unviredSDK.logDebug(
      'MasterDataPage',
      'fetchMasterData',
      `Fetching ${name}`
    );
    let fetchMasterDataQuery = ``;
    let headerName = '';
    switch (type) {
      case MasterDataType.structureTypes:
        this.structureTypesList = [];
        this.filteredStructureTypesList = [];
        headerName = 'STRUCTURE_TYPE_HEADER';
        fetchMasterDataQuery = `SELECT * FROM ${headerName} `;
        break;
      case MasterDataType.structureCategories:
        this.structureCategoriesList = [];
        this.filteredStructureCategoriesList = [];
        headerName = 'STRUCTURE_CAT_HEADER';
        fetchMasterDataQuery = `SELECT * FROM ${headerName} ORDER BY CATEGORY ASC`;
        break;
      case MasterDataType.structureStatus:
        this.structureStatusList = [];
        this.filteredStructureStatusList = [];
        headerName = 'STRUCTURE_STATUS_HEADER';
        fetchMasterDataQuery = `SELECT * FROM ${headerName} ORDER BY STATUS ASC`;
        break;
      case MasterDataType.roles:
        this.rolesList = [];
        this.filteredRolesList = [];
        headerName = 'ROLE_HEADER';
        fetchMasterDataQuery = `SELECT * FROM ${headerName} ORDER BY ROLE_NAME ASC`;
        break;

      case MasterDataType.permitTypes:
        this.permitTypesList = [];
        this.filteredPermitTypesList = [];
        headerName = 'PERMIT_TYPE_HEADER';
        fetchMasterDataQuery = `SELECT * FROM ${headerName} ORDER BY PERMIT_TYPE ASC`;
        break;

      case MasterDataType.approvalTypes:
        this.approvalTypesList = [];
        this.filteredApprovalTypesList = [];
        headerName = 'APPROVAL_TYPE_HEADER';
        fetchMasterDataQuery = `SELECT * FROM ${headerName} ORDER BY APPR_TYPE ASC`;
        break;

       case MasterDataType.skill:
            this.skillsList = [];
            this.filteredSkillsList = [];
            headerName = 'SKILL_HEADER';
            fetchMasterDataQuery = `SELECT * FROM ${headerName} ORDER BY SKILL_TYPE ASC`;
            break;

    }

    if (headerName == 'STRUCTURE_TYPE_HEADER') {
      // To sort the structures based on the order of elements.
      fetchMasterDataQuery += ' ORDER BY LENGTH(STRUCT_TYPE), STRUCT_TYPE';
    }

    this.unviredSDK.logDebug(
      'MasterDataPage',
      'fetchMasterData',
      `Fetching Query ${fetchMasterDataQuery}`
    );
    await this.unviredSDK.dbExecuteStatement(fetchMasterDataQuery).then(

      async (result: any) => {

        if (result.type == ResultType.success) {
          if (result.data.length > 0) {
            switch (type) {
              case MasterDataType.structureTypes:
                this.structureTypesList = result.data;
                this.filteredStructureTypesList = [...result.data];
                break;
              case MasterDataType.structureCategories:
                this.structureCategoriesList = result.data;
                this.filteredStructureCategoriesList = [...result.data];
                break;
              case MasterDataType.structureStatus:
                result.data.forEach((element) => {
                  if (element.IS_ACTIVE == 'X') {
                    element.IS_ACTIVE = true;
                  } else {
                    element.IS_ACTIVE = false;
                  }
                });
                this.structureStatusList = result.data;
                this.filteredStructureStatusList = [...result.data];
                break;
              case MasterDataType.roles:
                result.data.forEach((element: any) => {
                  element.IS_INTERNAL =
                    element.IS_INTERNAL == 'true' ? true : false;
                  element.CONFIGURATION =
                    element.CONFIGURATION == 'true' ? true : false;
                  element.FACILITY_MGMT =
                    element.FACILITY_MGMT == 'true' ? true : false;
                  element.USER_MGMT =
                    element.USER_MGMT == 'true' ? true : false;
                  element.AGENT_MGMT =
                    element.AGENT_MGMT == 'true' ? true : false;
                  element.PROC_MGMT =
                    element.PROC_MGMT == 'true' ? true : false;
                  element.REQUEST = element.REQUEST == 'true' ? true : false;
                  element.APPROVE = element.APPROVE == 'true' ? true : false;
                  element.ISSUE = element.ISSUE == 'true' ? true : false;
                  element.EXTEND = element.EXTEND == 'true' ? true : false;
                  element.CLOSE = element.CLOSE == 'true' ? true : false;
                  element.CANCEL = element.CANCEL == 'true' ? true : false;
                  element.REPORT = element.REPORT == 'true' ? true : false;
                  element.REVIEW = element.REVIEW == 'true' ? true : false;
                  element.EXECUTE = element.EXECUTE == 'true' ? true : false;
                });
                this.rolesList = result.data;
                this.filteredRolesList = [...result.data];
                this.addChangeKey();
                break;
              case MasterDataType.permitTypes:
                this.permitTypesList = result.data;
                this.filteredPermitTypesList = [...result.data];
                break;
              case MasterDataType.approvalTypes:
                this.approvalTypesList = result.data;
                this.filteredApprovalTypesList = [...result.data];
                break;

              case MasterDataType.skill:
                this.skillsList = result.data;
                this.filteredSkillsList = [...result.data];
                break;
            }
          }
        } else {
          this.unviredSDK.logError(
            'MasterDataPage',
            'fetchMasterData',
            `Error while fetching ${name} - ${result.message}`
          );
        }
      },
      (error) => {
        this.unviredSDK.logError(
          'MasterDataPage',
          'fetchMasterData',
          `Error while fetching ${name} - ${error.message}`
        );
      }
    );
  }

  async refreshSelectedMasterData() {
    await this.displayPleaseWaitLoader();
    switch (this.selectedTab) {
      case MasterDataType.structureTypes:
        let structureTypesResponse: any =
          await this.dataService.getStructureTypes();
        if (structureTypesResponse.type == ResultType.success) {
          await this.fetchMasterData(
            MasterDataType.structureTypes,
            'Structure Types',
            false
          );
        }
        break;
      case MasterDataType.structureCategories:
        let structureCategoriesResponse: any =
          await this.dataService.getStructureCategories();
        if (structureCategoriesResponse.type == ResultType.success) {
          await this.fetchMasterData(
            MasterDataType.structureCategories,
            'Structure Categories',
            false
          );
        }
        break;
      case MasterDataType.structureStatus:
        let structureStatusResponse: any =
          await this.dataService.getStructureStatus();
        if (structureStatusResponse.type == ResultType.success) {
          await this.fetchMasterData(
            MasterDataType.structureStatus,
            'Structure Status',
            false
          );
        }
        break;
      case MasterDataType.roles:
        this.loadingController.dismiss();
        // let rolesResponse: any = await this.dataService.getRoles();
        // if (rolesResponse.type == ResultType.success) {
        //   await this.fetchMasterData(MasterDataType.roles, 'Roles', false);
        // }
        break;
      case MasterDataType.permitTypes:
        let permitTypesResponse: any =
          await this.dataService.getPermitTypes();
        if (permitTypesResponse.type == ResultType.success) {
          await this.fetchMasterData(
            MasterDataType.permitTypes,
            'Permit Types',
            false
          );
        }
        break;
    }
    this.loadingController.dismiss();
  }

  // Display Loading dialog.
  async displayPleaseWaitLoader() {
    const loading = await this.loadingController.create({
      message: 'Please wait...',
      backdropDismiss: false,
    });
    await loading.present();
  }

  // Triggers when change master data type tabs.
  async segmentChanged(ev: any) {
    // Scroll to top when tab changes
    if (this.content) {
      this.content.scrollToTop(300);
    }

    // Reset search term when changing tabs
    this.searchTerm = '';

    // Show shimmer effect immediately
    this.isShowProgressBar = true;

    let value = ev.detail.value;
    switch (value) {
      case MasterDataType.structureTypes:
        this.title = 'Structure Types';
        await this.fetchMasterData(
          MasterDataType.structureTypes,
          'Structure Types',
          true
        );
        setTimeout(() => {
          this.isShowProgressBar = false;
        }, 300);
        break;
      case MasterDataType.structureCategories:
        this.title = 'Structure Categories';
        await this.fetchMasterData(
          MasterDataType.structureCategories,
          'Structure Categories',
          true
        );
        setTimeout(() => {
          this.isShowProgressBar = false;
        }, 300);
        break;
      case MasterDataType.structureStatus:
        this.title = 'Structure Status';
        await this.fetchMasterData(
          MasterDataType.structureStatus,
          'Structure Status',
          true
        );
        setTimeout(() => {
          this.isShowProgressBar = false;
        }, 300);
        break;
      case MasterDataType.roles:
        this.title = 'Roles';
        await this.fetchMasterData(MasterDataType.roles, 'Roles', true);
        setTimeout(() => {
          this.isShowProgressBar = false;
        }, 300);
        break;
      case MasterDataType.permitTypes:
        this.title = 'Permit Types';
        await this.fetchMasterData(
          MasterDataType.permitTypes,
          'Permit Types',
          true
        );
        setTimeout(() => {
          this.isShowProgressBar = false;
        }, 300);
        break;
      case MasterDataType.approvalTypes:
        this.title = 'Approval Types';
        await this.fetchMasterData(
          MasterDataType.approvalTypes,
          'Approval Types',
          true
        );
        setTimeout(() => {
          this.isShowProgressBar = false;
        }, 300);
        break;

        case MasterDataType.skill:
          this.title = 'Skills';
          await this.fetchMasterData(
            MasterDataType.skill,
            'Skill',
            true
          );
          setTimeout(() => {
            this.isShowProgressBar = false;
          }, 300);
          break;

      default:
        this.selectedTab = 'structureTypes';
        this.title = 'Structure Types';
        await this.fetchMasterData(
          MasterDataType.structureTypes,
          'Structure Types',
          true
        );
        setTimeout(() => {
          this.isShowProgressBar = false;
        }, 300);
        break;
    }
  }

  // Opening selected master data details with either edit or new mode.
  async openSelectedMaterDataDetailsModal(
    item: any,
    selectedMode: number,
    index: number
  ) {
    let cssClass = '';
    if (this.selectedTab == MasterDataType.roles) {
      cssClass = 'master-data-info-modal-roles';
    } else {
      cssClass = 'master-data-info-modal';
    }

    const modal = await this.modalController.create({
      component: MasterDataDetailsComponent,
      cssClass: cssClass,
      backdropDismiss: false,
      componentProps: {
        selectedTab: this.selectedTab,
        data: item,
        mode:
          selectedMode == 1 ? MasterSelectedMode.edit : MasterSelectedMode.new,
      },
    });
    modal.onDidDismiss().then(async (result) => {
      if (result.data) {
        switch (this.selectedTab) {
          case MasterDataType.structureTypes:
            if (result.data.status) {
              await this.fetchMasterData(
                MasterDataType.structureTypes,
                'Structure Types',
                false
              );
            }
            break;
          case MasterDataType.structureCategories:
            if (result.data.status) {
              await this.fetchMasterData(
                MasterDataType.structureCategories,
                'Structure Categories',
                false
              );
            }
            break;
          case MasterDataType.structureStatus:
            if (result.data.status) {
              await this.fetchMasterData(
                MasterDataType.structureStatus,
                'Structure Status',
                false
              );
            }
            break;
          case MasterDataType.roles:
            if (result.data.status) {
              await this.fetchMasterData(MasterDataType.roles, 'Roles', false);
            }
            break;

          case MasterDataType.approvalTypes:
            if (result.data.status) {
              await this.fetchMasterData(
                MasterDataType.approvalTypes,
                'Approval Types',
                false
              );
            }
            break;

          case MasterDataType.permitTypes:
            if (result.data.status) {
              // Refresh from server to get updated icon and color data
              let permitTypesResponse: any = await this.dataService.getPermitTypes();
              if (permitTypesResponse.type == ResultType.success) {
                // Add a small delay to ensure database synchronization is complete
                setTimeout(async () => {
                  await this.fetchMasterData(
                    MasterDataType.permitTypes,
                    'Permit Types',
                    false
                  );
                }, 100);
              }
            }
            break;


            case MasterDataType.skill:
              if (result.data.status) {
                await this.fetchMasterData(
                  MasterDataType.skill,
                  'Skill',
                  false
                );
              }
        }
      }
    });
    return await modal.present();
  }

  // Delete selected item based on selected type.
  async deleteSelectedItem(item: any, selectedMode: any) {
    console.log("item && selectedMode" , item , selectedMode)
    const alert = await this.alertController.create({
      header: 'Delete',
      message: 'Are you sure you want to delete this item' + '?',
      buttons: [
        {
          text: 'No',
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => { },
        },
        {
          text: 'Yes',
          handler: async () => {
            switch (selectedMode) {
              case MasterDataType.structureTypes:
                await this.displayPleaseWaitLoader();

                let structureType = new STRUCTURE_TYPE_HEADER();
                structureType.DESCRIPTION = item.DESCRIPTION;
                structureType.STRUCT_TYPE = item.STRUCT_TYPE;
                structureType.P_MODE = 'D';

                let structureTypeInput = {
                  STRUCTURE_TYPE: [
                    {
                      STRUCTURE_TYPE_HEADER: structureType,
                    },
                  ],
                };

                let structureTypesResult: any =
                  await this.dataService.modifyStructureType(
                    structureTypeInput
                  );
                if (structureTypesResult.type == ResultType.success) {
                  await this.fetchMasterData(
                    MasterDataType.structureTypes,
                    'Structure Types',
                    false
                  );
                  this.loadingController.dismiss();
                } else {
                  await this.dataService.displayErrorMessageDialog(
                    structureTypesResult.message
                  );
                  this.loadingController.dismiss();
                }
                break;
              case MasterDataType.structureCategories:
                await this.displayPleaseWaitLoader();

                let structureCategories = new STRUCTURE_CAT_HEADER();
                structureCategories.DESCRIPTION = item.DESCRIPTION;
                structureCategories.CATEGORY = item.CATEGORY;
                structureCategories.P_MODE = 'D';

                let structureCategoriesInput = {
                  STRUCTURE_CAT: [
                    {
                      STRUCTURE_CAT_HEADER: structureCategories,
                    },
                  ],
                };

                let structureCategoriesResult: any =
                  await this.dataService.modifyStructureCategories(
                    structureCategoriesInput
                  );
                if (structureCategoriesResult.type == ResultType.success) {
                  await this.fetchMasterData(
                    MasterDataType.structureCategories,
                    'Structure Categories',
                    false
                  );
                  this.loadingController.dismiss();
                } else {
                  await this.dataService.displayErrorMessageDialog(
                    structureCategoriesResult.message
                  );
                  this.loadingController.dismiss();
                }
                break;
              case MasterDataType.structureStatus:
                await this.displayPleaseWaitLoader();

                let structureStatus = new STRUCTURE_STATUS_HEADER();
                structureStatus.DESCRIPTION = item.DESCRIPTION;
                structureStatus.STATUS = item.STATUS;
                structureStatus.P_MODE = 'D';
                if (item.IS_ACTIVE == true) {
                  structureStatus.IS_ACTIVE = 'X';
                } else {
                  structureStatus.IS_ACTIVE = '';
                }

                let structureStatusInput = {
                  STRUCTURE_STATUS: [
                    {
                      STRUCTURE_STATUS_HEADER: structureStatus,
                    },
                  ],
                };

                let structureStatusResult: any =
                  await this.dataService.modifyStructureStatus(
                    structureStatusInput
                  );
                if (structureStatusResult.type == ResultType.success) {
                  await this.fetchMasterData(
                    MasterDataType.structureStatus,
                    'Structure Status',
                    false
                  );
                  this.loadingController.dismiss();
                } else {
                  await this.dataService.displayErrorMessageDialog(
                    structureStatusResult.message
                  );
                  this.loadingController.dismiss();
                }
                break;
              case MasterDataType.roles:
                await this.displayPleaseWaitLoader();
                let roles = new ROLE_HEADER();
                roles.ROLE_NAME = item.ROLE_NAME;
                roles.IS_INTERNAL = item.IS_INTERNAL == true ? 'true' : 'false';
                roles.CONFIGURATION =
                  item.CONFIGURATION == true ? 'true' : 'false';
                roles.FACILITY_MGMT =
                  item.FACILITY_MGMT == true ? 'true' : 'false';
                roles.USER_MGMT = item.USER_MGMT == true ? 'true' : 'false';
                roles.AGENT_MGMT = item.AGENT_MGMT == true ? 'true' : 'false';
                roles.PROC_MGMT = item.PROC_MGMT == true ? 'true' : 'false';
                roles.REQUEST = item.REQUEST == true ? 'true' : 'false';
                roles.APPROVE = item.APPROVE == true ? 'true' : 'false';
                roles.ISSUE = item.ISSUE == true ? 'true' : 'false';
                roles.EXTEND = item.EXTEND == true ? 'true' : 'false';
                roles.EXECUTE = item.EXECUTE == true ? 'true' : 'false';
                roles.CLOSE = item.CLOSE == true ? 'true' : 'false';
                roles.CANCEL = item.CANCEL == true ? 'true' : 'false';
                roles.REPORT = item.REPORT == true ? 'true' : 'false';
                roles.REVIEW = item.REVIEW == true ? 'true' : 'false';
                roles.P_MODE = 'D';

                let rolesInput = {
                  ROLE: [
                    {
                      ROLE_HEADER: roles,
                    },
                  ],
                };

                let rolesResult: any = await this.dataService.modifyRoles(
                  rolesInput
                );
                if (rolesResult.type == ResultType.success) {
                  await this.fetchMasterData(
                    MasterDataType.roles,
                    'Roles',
                    false
                  );
                  this.loadingController.dismiss();
                } else {
                  await this.dataService.displayErrorMessageDialog(
                    rolesResult.message
                  );
                  this.loadingController.dismiss();
                }
                break;
              case MasterDataType.approvalTypes:
                await this.displayPleaseWaitLoader();
                let approvalType = new APPROVAL_TYPE_HEADER();
                approvalType.DESCRIPTION = item.DESCRIPTION;
                approvalType.APPR_TYPE = item.APPR_TYPE;
                approvalType.SCOPE = item.SCOPE;
                approvalType.P_MODE = 'D';

                let approvalTypeInput = {
                  APPROVAL_TYPE: [
                    {
                      APPROVAL_TYPE_HEADER: approvalType,
                    },
                  ],
                };

                let approvalTypesResult: any =
                  await this.dataService.modifyApprovalType(approvalTypeInput);
                if (approvalTypesResult.type == ResultType.success) {
                  await this.fetchMasterData(
                    MasterDataType.approvalTypes,
                    'Approval Types',
                    false
                  );
                  this.loadingController.dismiss();
                } else {
                  await this.dataService.displayErrorMessageDialog(
                    approvalTypesResult.message
                  );
                  this.loadingController.dismiss();
                }
                break;
              case MasterDataType.permitTypes:
                await this.displayPleaseWaitLoader();
                let permitType = new PERMIT_TYPE_HEADER();
                permitType.DESCRIPTION = item.DESCRIPTION;
                permitType.PERMIT_TYPE = item.PERMIT_TYPE;
                permitType.PREFIX = item.PREFIX;
                permitType.FORM_ID = item.FORM_ID;
                permitType.FORM_NAME = item.FORM_NAME;
                permitType.P_MODE = 'D';

                let permitTypeInput = {
                  PERMIT_TYPE: [
                    {
                      PERMIT_TYPE_HEADER: permitType,
                    },
                  ],
                };


                //  if (included?.length > 0) {
                //             for (let f = 0; f < included.length; f++) {
                //               let userApprovalHeader = {} as PERMIT_TYPE_APPROVAL;
                //               userApprovalHeader.P_MODE = 'D';
                //               userApprovalHeader.PERMIT_TYPE = included[f].PERMIT_TYPE
                //               userApprovalHeader.APPR_TYPE = included[f].APPR_TYPE
                //               temp.push(userApprovalHeader)
                //             }
                //           }



                let permitTypesResult: any =
                  await this.dataService.modifyPermitType(permitTypeInput);
                if (permitTypesResult.type == ResultType.success) {
                  await this.fetchMasterData(
                    MasterDataType.permitTypes,
                    'Permit Types',
                    false
                  );
                  this.loadingController.dismiss();
                } else {
                  await this.dataService.displayErrorMessageDialog(
                    permitTypesResult.message
                  );
                  this.loadingController.dismiss();
                }
                break;

                case MasterDataType.skill:
                  await this.displayPleaseWaitLoader();
                  let skillType = new SKILL_HEADER();
                  skillType.DESCRIPTION = item.DESCRIPTION;
                  skillType.SKILL_TYPE = item.SKILL_TYPE;
                  skillType.P_MODE = 'D';

                  let skillTypeInput = {
                    SKILL: [
                      {
                        SKILL_HEADER: skillType,
                      },
                    ],
                  };

                  let skillTypesResult: any =
                    await this.dataService.modifySkillType(skillTypeInput);
                  if (skillTypesResult.type == ResultType.success) {
                    await this.fetchMasterData(
                      MasterDataType.skill,
                      'Skill',
                      false
                    );
                    this.loadingController.dismiss();
                  } else {
                    await this.dataService.displayErrorMessageDialog(
                      skillTypesResult.message
                    );
                    this.loadingController.dismiss();
                  }
                  break;
            }
          },
        },
      ],
    });
    await alert.present();
  }

  async structureStatusIsActiveChange(item: any) {
    await this.displayPleaseWaitLoader();

    let structureStatus = new STRUCTURE_STATUS_HEADER();
    structureStatus.DESCRIPTION = item.DESCRIPTION;
    structureStatus.STATUS = item.STATUS;
    structureStatus.P_MODE = 'M';
    if (item.IS_ACTIVE == true) {
      structureStatus.IS_ACTIVE = 'X';
    } else {
      structureStatus.IS_ACTIVE = '';
    }

    let structureStatusInput = {
      STRUCTURE_STATUS: [
        {
          STRUCTURE_STATUS_HEADER: structureStatus,
        },
      ],
    };

    let structureStatusResult: any =
      await this.dataService.modifyStructureStatus(structureStatusInput);
    if (structureStatusResult.type == ResultType.success) {
      await this.fetchMasterData(
        MasterDataType.structureStatus,
        'Structure Status',
        false
      );
      this.loadingController.dismiss();
    } else {
      await this.dataService.displayErrorMessageDialog(
        structureStatusResult.message
      );
      this.loadingController.dismiss();
    }
  }

  //modifying the roles
  // mark the object as changed and set to p_mode=m
  async roleChange(item: any) {
    item.isChanged = true;
    item.P_MODE = 'M';
    item.ROLE_NAME;
    item.IS_INTERNAL === true ? 'true' : 'false';
    item.CONFIGURATION === true ? 'true' : 'false';
    item.FACILITY_MGMT === true ? 'true' : 'false';
    item.USER_MGMT === true ? 'true' : 'false';
    item.AGENT_MGMT === true ? 'true' : 'false';
    item.PROC_MGMT === true ? 'true' : 'false';
    item.REQUEST === true ? 'true' : 'false';
    item.APPROVE === true ? 'true' : 'false';
    item.ISSUE === true ? 'true' : 'false';
    item.EXTEND === true ? 'true' : 'false';
    item.EXECUTE === true ? 'true' : 'false';
    item.CLOSE === true ? 'true' : 'false';
    item.CANCEL === true ? 'true' : 'false';
    item.REPORT === true ? 'true' : 'false';
    item.REVIEW === true ? 'true' : 'false';
  }

  // By default all obj roles will be treated as not changed
  addChangeKey() {
    this.rolesList.forEach((role: any) => {
      role.isChanged = false;
    });
  }

  // Save Roles to modify
  async saveRoles() {
    await this.displayPleaseWaitLoader();
    // getting the changed object by checking isChanged key
    let rolesChangedObject = this.rolesList.filter((role: any) => {
      return role.isChanged;
    });

    // deleting the isChanged key before sending to api
    rolesChangedObject = rolesChangedObject.map((obj: any) => {
      delete obj.isChanged;
      return obj;
    });
    let tempArr = [];
    rolesChangedObject.forEach(async (item: any) => {
      let roles = new ROLE_HEADER();
      roles.ROLE_NAME = item.ROLE_NAME;
      roles.IS_INTERNAL = item.IS_INTERNAL == true ? 'true' : 'false';
      roles.CONFIGURATION = item.CONFIGURATION == true ? 'true' : 'false';
      roles.FACILITY_MGMT = item.FACILITY_MGMT == true ? 'true' : 'false';
      roles.USER_MGMT = item.USER_MGMT == true ? 'true' : 'false';
      roles.AGENT_MGMT = item.AGENT_MGMT == true ? 'true' : 'false';
      roles.PROC_MGMT = item.PROC_MGMT == true ? 'true' : 'false';
      roles.REQUEST = item.REQUEST == true ? 'true' : 'false';
      roles.APPROVE = item.APPROVE == true ? 'true' : 'false';
      roles.ISSUE = item.ISSUE == true ? 'true' : 'false';
      roles.EXTEND = item.EXTEND == true ? 'true' : 'false';
      roles.EXECUTE = item.EXECUTE == true ? 'true' : 'false';
      roles.CLOSE = item.CLOSE == true ? 'true' : 'false';
      roles.CANCEL = item.CANCEL == true ? 'true' : 'false';
      roles.REPORT = item.REPORT == true ? 'true' : 'false';
      roles.REVIEW = item.REVIEW == true ? 'true' : 'false';
      roles.P_MODE = 'M';
      let rolesInput = {
        ROLE: [
          {
            ROLE_HEADER: roles,
          },
        ],
      };

      let rolesResult: any = await this.dataService.modifyRoles(rolesInput);
      console.log('role list in roleChanging in table', rolesResult);
      if (rolesResult.type == ResultType.success) {
        await this.fetchMasterData(MasterDataType.roles, 'Roles', false);
        this.loadingController.dismiss();
        await this.modalController.dismiss({ status: true });
      } else {
        await this.dataService.displayErrorMessageDialog(rolesResult.message);
        this.loadingController.dismiss();
      }
    });
    this.loadingController?.dismiss();
  }
}
